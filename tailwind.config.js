/** @type {import('tailwindcss').Config} */
export default {
  content: ["./src/**/*.{html,js,ts}"],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#6E56CF', // Deep Indigo
          50: '#EDE9F8',      // Lavender Mist
          100: '#DDD5F3',
          200: '#C4B5EA',
          300: '#AB95E1',
          400: '#9275D8',
          500: '#6E56CF',     // Primary
          600: '#5A44B8',
          700: '#4A3899',
          800: '#3A2C7A',
          900: '#2A205B',
        },
        gray: {
          50: '#F2F2F7',      // Gris clair Apple-like
          100: '#E5E5EA',     // Apple gray 100
          200: '#D1D1D6',     // Apple gray 200
          300: '#C7C7CC',     // Apple gray 300
          400: '#AEAEB2',     // Apple gray 400
          500: '#8E8E93',     // Apple gray 500
          600: '#636366',     // Apple gray 600
          700: '#48484A',     // Apple gray 700
          800: '#3A3A3C',     // Apple gray 800
          900: '#1C1C1E',     // Gris foncé Apple-like
        }
      },
      fontFamily: {
        sans: ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'SF Pro Text', 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', 'sans-serif'],
      },
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1.1' }],
        '6xl': ['3.75rem', { lineHeight: '1.1' }],
        '7xl': ['4.5rem', { lineHeight: '1.1' }],
        '8xl': ['6rem', { lineHeight: '1.1' }],
        '9xl': ['8rem', { lineHeight: '1.1' }],
      },
      letterSpacing: {
        tighter: '-0.05em',
        tight: '-0.025em',
        normal: '0',
        wide: '0.025em',
        wider: '0.05em',
        widest: '0.1em',
      },
    },
  },
  plugins: [],
}
