# 📧 Configuration EmailJS pour Workeem

Ce guide vous explique comment configurer EmailJS pour recevoir les demandes de contact depuis votre landing page.

## 🚀 Étapes de Configuration

### 1. Créer un compte EmailJS
1. Allez sur [https://www.emailjs.com/](https://www.emailjs.com/)
2. Créez un compte gratuit
3. Confirmez votre email

### 2. Configurer un service email
1. Dans le dashboard, allez dans **Email Services**
2. Cliquez sur **Add New Service**
3. Choisissez votre fournisseur (Gmail, Outlook, etc.)
4. Suivez les instructions pour connecter votre email
5. Notez votre **Service ID** (ex: `service_abc123`)

### 3. Créer un template d'email
1. Allez dans **Email Templates**
2. Cliquez sur **Create New Template**
3. Utilisez ce template :

```
Sujet: Nouvelle demande de démo - {{company}}

Bonjour {{to_name}},

Vous avez reçu une nouvelle demande de démo de la part de :

👤 Nom: {{from_name}}
📧 Email: {{from_email}}
🏢 Entreprise: {{company}}
📞 Téléphone: {{phone}}

💬 Message:
{{message}}

---
Envoyé depuis le formulaire de contact Workeem
```

4. Testez le template
5. Notez votre **Template ID** (ex: `template_xyz789`)

### 4. Obtenir votre clé publique
1. Allez dans **Account** > **General**
2. Copiez votre **Public Key** (ex: `user_abcdef123456`)

### 5. Configurer l'application
1. Ouvrez le fichier `src/app/config/emailjs.config.ts`
2. Remplacez les valeurs par vos vraies clés :

```typescript
export const EMAILJS_CONFIG = {
  SERVICE_ID: 'service_abc123',      // Votre Service ID
  TEMPLATE_ID: 'template_xyz789',    // Votre Template ID
  PUBLIC_KEY: 'user_abcdef123456'    // Votre Public Key
};
```

## 🧪 Test de Configuration

1. Redémarrez votre serveur Angular : `npm start`
2. Allez sur votre landing page
3. Remplissez le formulaire de contact
4. Cliquez sur "Demander une démo"
5. Vérifiez votre boîte email

## 📊 Variables du Template

Le formulaire envoie ces variables à EmailJS :

| Variable | Description | Exemple |
|----------|-------------|---------|
| `{{from_name}}` | Prénom + Nom | "Marie Dubois" |
| `{{from_email}}` | Email du contact | "<EMAIL>" |
| `{{company}}` | Nom de l'espace | "CoWork Paris" |
| `{{phone}}` | Téléphone | "+33 1 23 45 67 89" |
| `{{message}}` | Message personnalisé | "Nous avons 50 postes..." |
| `{{to_name}}` | Destinataire | "Équipe Workeem" |

## 🔧 Dépannage

### Erreur "Service ID not found"
- Vérifiez que votre Service ID est correct
- Assurez-vous que le service est activé

### Erreur "Template not found"
- Vérifiez que votre Template ID est correct
- Assurez-vous que le template est publié

### Erreur "Public Key invalid"
- Vérifiez votre Public Key
- Régénérez-la si nécessaire

### Emails non reçus
- Vérifiez vos spams
- Testez depuis le dashboard EmailJS
- Vérifiez la configuration de votre service email

## 💡 Conseils

1. **Quota gratuit** : 200 emails/mois
2. **Sécurité** : Les clés sont publiques, c'est normal
3. **Monitoring** : Surveillez vos envois depuis le dashboard
4. **Backup** : Configurez plusieurs services email

## 🎯 Prochaines Étapes

Une fois EmailJS configuré :
1. Testez le formulaire
2. Personnalisez le template
3. Configurez des notifications
4. Ajoutez Google Analytics
5. Optimisez le SEO

---

✅ **Configuration terminée !** Votre formulaire de contact est maintenant opérationnel.
