# 📧 Template EmailJS Amélioré pour Workeem

## **Sujet du template :**
```
Nouvelle demande de démo Workeem - {{company}}
```

## **Corps du template (HTML amélioré) :**
```html
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nouvelle demande Workeem</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1C1C1E;
            background-color: #F2F2F7;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #6E56CF 0%, #5A44B8 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            padding: 30px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .info-item {
            background: #F2F2F7;
            padding: 15px;
            border-radius: 12px;
            border-left: 4px solid #6E56CF;
        }
        .info-label {
            font-size: 12px;
            font-weight: 600;
            color: #8E8E93;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }
        .info-value {
            font-size: 16px;
            font-weight: 500;
            color: #1C1C1E;
        }
        .message-section {
            background: #EDE9F8;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 4px solid #6E56CF;
        }
        .message-label {
            font-size: 12px;
            font-weight: 600;
            color: #6E56CF;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 10px;
        }
        .message-content {
            font-size: 16px;
            line-height: 1.5;
            color: #1C1C1E;
            font-style: italic;
        }
        .footer {
            background: #F2F2F7;
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid #E5E5EA;
        }
        .footer p {
            margin: 0;
            font-size: 14px;
            color: #8E8E93;
        }
        .cta-button {
            display: inline-block;
            background: #6E56CF;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            margin: 15px 0;
            transition: background 0.3s ease;
        }
        .cta-button:hover {
            background: #5A44B8;
        }
        .empty-field {
            color: #8E8E93;
            font-style: italic;
        }
        @media (max-width: 600px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
            .container {
                margin: 10px;
                border-radius: 12px;
            }
            .header, .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Nouvelle demande de démo</h1>
            <p>Un prospect souhaite découvrir Workeem</p>
        </div>
        
        <div class="content">
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Contact</div>
                    <div class="info-value">{{from_name}}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Email</div>
                    <div class="info-value">{{from_email}}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Espace de coworking</div>
                    <div class="info-value">
                        {{#if company}}{{company}}{{else}}<span class="empty-field">Non renseigné</span>{{/if}}
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">Téléphone</div>
                    <div class="info-value">{{phone}}</div>
                </div>
            </div>
            
            {{#if message}}
            <div class="message-section">
                <div class="message-label">💬 Message du prospect</div>
                <div class="message-content">
                    "{{message}}"
                </div>
            </div>
            {{else}}
            <div class="message-section">
                <div class="message-label">💬 Message du prospect</div>
                <div class="message-content empty-field">
                    Aucun message spécifique
                </div>
            </div>
            {{/if}}
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="mailto:{{from_email}}" class="cta-button">
                    📧 Répondre au prospect
                </a>
            </div>
        </div>
        
        <div class="footer">
            <p>📅 Demande reçue le {{date}} via le formulaire Workeem</p>
            <p style="margin-top: 10px; font-size: 12px;">
                💡 <strong>Conseil :</strong> Répondez dans les 2h pour maximiser vos chances de conversion
            </p>
        </div>
    </div>
</body>
</html>
```

## **Version Text (Fallback) :**
```
🚀 NOUVELLE DEMANDE DE DÉMO WORKEEM

👤 Contact: {{from_name}}
📧 Email: {{from_email}}
🏢 Espace: {{#if company}}{{company}}{{else}}Non renseigné{{/if}}
📞 Téléphone: {{phone}}

💬 Message:
{{#if message}}"{{message}}"{{else}}Aucun message spécifique{{/if}}

---
📅 Demande reçue le {{date}} via le formulaire Workeem
💡 Répondez rapidement pour maximiser vos chances de conversion !

Répondre: {{from_email}}
```

## **Améliorations du Template :**

1. **Gestion des champs vides** avec `{{#if}}` pour company et message
2. **Style spécial** pour les champs non renseignés (`.empty-field`)
3. **Téléphone toujours affiché** car maintenant obligatoire
4. **Meilleure lisibilité** avec des conditions logiques

## **Variables EmailJS :**

- `{{from_name}}` - Prénom + Nom (toujours rempli)
- `{{from_email}}` - Email (toujours rempli)
- `{{company}}` - Nom de l'espace (peut être vide)
- `{{phone}}` - Téléphone (maintenant obligatoire)
- `{{message}}` - Message (peut être vide)
- `{{date}}` - Date automatique

Ce template gère maintenant intelligemment les champs vides ! 📧✨
