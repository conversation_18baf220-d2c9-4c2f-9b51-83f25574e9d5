@tailwind base;
@tailwind components;
@tailwind utilities;

/* Styles globaux pour Workeem - Apple-like */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'kern' 1;
    text-rendering: optimizeLegibility;
    letter-spacing: -0.01em;
  }

  /* Apple-like typography */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: -0.02em;
    line-height: 1.1;
  }

  /* Smooth transitions globales */
  * {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@layer components {
  /* Boutons Apple-like */
  .btn-primary {
    @apply bg-primary hover:bg-primary-600 text-white px-8 py-3 rounded-full font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply border-2 border-gray-300 text-gray-700 hover:border-primary hover:text-primary px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:shadow-md;
  }

  /* Cards Apple-like */
  .card {
    @apply bg-white rounded-2xl shadow-sm hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1 border border-gray-100 hover:border-gray-200;
  }

  /* Typography Apple-like */
  .section-title {
    @apply text-4xl md:text-5xl font-bold text-gray-900 mb-6 tracking-tight;
  }

  .section-subtitle {
    @apply text-xl text-gray-600 max-w-3xl mx-auto font-light;
  }

  /* Glassmorphism effect */
  .glass {
    @apply bg-white/70 backdrop-blur-xl border border-white/20;
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary to-primary-600 bg-clip-text text-transparent;
  }

  /* Apple-like shadows */
  .shadow-apple {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 0 0 1px rgba(0, 0, 0, 0.05);
  }

  .shadow-apple-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(0, 0, 0, 0.05);
  }

  /* Micro-interactions Apple-like */
  .hover-lift {
    @apply transition-all duration-300 ease-out;
  }

  .hover-lift:hover {
    @apply transform -translate-y-1 shadow-2xl;
  }

  /* Smooth scroll pour les ancres */
  html {
    scroll-padding-top: 80px;
  }

  /* Animation d'apparition */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  /* Effet de focus Apple-like */
  button:focus,
  a:focus {
    outline: 2px solid #6E56CF;
    outline-offset: 2px;
  }
}

