<!-- Header -->
<header class="bg-white/80 backdrop-blur-xl border-b border-gray-100 sticky top-0 z-50 relative">
  <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo -->
      <div class="flex-shrink-0">
        <button (click)="scrollToTop()" class="focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 rounded-lg p-1 transition-all duration-300 hover:scale-105">
          <img class="h-12 w-auto" src="images/logo.png" alt="Workeem">
        </button>
      </div>

      <!-- Navigation Links -->
      <div class="hidden md:block">
        <div class="ml-10 flex items-baseline space-x-8">
          <a href="#features" class="text-gray-900 hover:text-primary transition-all duration-300 font-medium text-sm">Fonctionnalités</a>
          <a href="#pricing" class="text-gray-900 hover:text-primary transition-all duration-300 font-medium text-sm">Tarifs</a>
          <a href="#why-choose" class="text-gray-900 hover:text-primary transition-all duration-300 font-medium text-sm">Avantages</a>
          <a href="#contact-form" class="text-gray-900 hover:text-primary transition-all duration-300 font-medium text-sm">Contact</a>
        </div>
      </div>

      <!-- CTA Button -->
      <div class="hidden md:block">
        <div class="flex items-center space-x-4">
          <a href="https://eworkeem.netlify.app" target="_blank" class="text-gray-900 hover:text-primary transition-all duration-300 font-medium text-sm">
            Se connecter
          </a>
          <a href="#contact-form" class="bg-primary hover:bg-primary-600 text-white px-6 py-2.5 rounded-full font-medium text-sm transition-all duration-300 shadow-sm hover:shadow-md inline-block">
            Essai gratuit
          </a>
        </div>
      </div>

      <!-- Mobile menu button -->
      <div class="md:hidden">
        <button type="button"
                (click)="toggleMobileMenu()"
                class="text-gray-900 hover:text-primary transition-all duration-300 p-2 rounded-lg hover:bg-gray-50 relative z-50">
          <!-- Hamburger Icon -->
          <div class="w-5 h-5 flex flex-col justify-center items-center">
            <span class="block w-5 h-0.5 bg-current transition-all duration-300 transform"
                  [class.rotate-45]="isMobileMenuOpen"
                  [class.translate-y-1.5]="isMobileMenuOpen"></span>
            <span class="block w-5 h-0.5 bg-current mt-1 transition-all duration-300"
                  [class.opacity-0]="isMobileMenuOpen"></span>
            <span class="block w-5 h-0.5 bg-current mt-1 transition-all duration-300 transform"
                  [class.-rotate-45]="isMobileMenuOpen"
                  [class.-translate-y-1.5]="isMobileMenuOpen"></span>
          </div>
        </button>
      </div>
    </div>

    <!-- Mobile Menu Dropdown -->
    <div class="md:hidden absolute top-full left-0 right-0 bg-white border-t border-gray-100 shadow-lg transform transition-all duration-300 ease-in-out z-50"
         [class.max-h-0]="!isMobileMenuOpen"
         [class.max-h-96]="isMobileMenuOpen"
         [class.opacity-0]="!isMobileMenuOpen"
         [class.opacity-100]="isMobileMenuOpen"
         style="overflow: hidden;">

      <nav class="px-4 py-6">
        <div class="space-y-4">
          <a href="#features"
             (click)="closeMobileMenu()"
             class="block text-gray-900 hover:text-primary transition-colors duration-300 py-3 px-4 rounded-lg hover:bg-gray-50 font-medium">
            Fonctionnalités
          </a>
          <a href="#pricing"
             (click)="closeMobileMenu()"
             class="block text-gray-900 hover:text-primary transition-colors duration-300 py-3 px-4 rounded-lg hover:bg-gray-50 font-medium">
            Tarifs
          </a>
          <a href="#why-choose"
             (click)="closeMobileMenu()"
             class="block text-gray-900 hover:text-primary transition-colors duration-300 py-3 px-4 rounded-lg hover:bg-gray-50 font-medium">
            Avantages
          </a>
          <a href="#contact-form"
             (click)="closeMobileMenu()"
             class="block text-gray-900 hover:text-primary transition-colors duration-300 py-3 px-4 rounded-lg hover:bg-gray-50 font-medium">
            Contact
          </a>
        </div>

        <!-- CTA Button Mobile -->
        <div class="mt-6 px-4 space-y-3">
          <a href="https://eworkeem.netlify.app" target="_blank"
             (click)="closeMobileMenu()"
             class="block w-full border-2 border-primary text-primary text-center px-6 py-3 rounded-full font-medium transition-all duration-300 hover:bg-primary hover:text-white">
            Se connecter
          </a>
          <a href="#contact-form"
             (click)="closeMobileMenu()"
             class="block w-full bg-primary hover:bg-primary-600 text-white text-center px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-sm hover:shadow-md">
            Essai gratuit
          </a>
        </div>
      </nav>
    </div>
  </nav>
</header>

<!-- Hero Section -->
<section class="bg-gradient-to-br from-primary-50 via-white to-gray-50 py-24 md:py-32 overflow-hidden"
         itemscope itemtype="https://schema.org/WebPageElement"
         aria-label="Section principale - Présentation de Workeem">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center">
      <h1 class="text-5xl md:text-7xl font-bold text-gray-900 mb-8 tracking-tight leading-none"
          itemprop="headline">
        Gérez votre espace de
        <span class="text-primary bg-gradient-to-r from-primary to-primary-600 bg-clip-text text-transparent">coworking</span>
        <br class="hidden md:block"> en toute simplicité
      </h1>
      <p class="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto font-light leading-relaxed">
        Workeem vous offre tous les outils nécessaires pour optimiser la gestion de votre espace de coworking :
        membres, réservations, facturation et bien plus encore.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
        <a href="#contact-form" class="bg-primary hover:bg-primary-600 text-white px-10 py-4 rounded-full font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 inline-block">
          Commencer gratuitement
        </a>
        <a href="#contact-form" class="border-2 border-gray-300 text-gray-700 hover:border-primary hover:text-primary px-10 py-4 rounded-full font-semibold text-lg transition-all duration-300 hover:shadow-md inline-block">
          Voir la démo
        </a>
      </div>
      <p class="text-sm text-gray-500 mb-16">Essai gratuit de 14 jours • Aucune carte de crédit requise</p>

      <!-- Hero Image - SaaS Dashboard Screenshot -->
      <div class="relative max-w-6xl mx-auto">
        <div class="relative bg-white rounded-3xl shadow-2xl border border-gray-200 overflow-hidden transform perspective-1000 rotate-x-2 hover:rotate-x-0 transition-all duration-700">
          <!-- Browser Chrome -->
          <div class="bg-gray-100 px-6 py-4 border-b border-gray-200">
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-red-400 rounded-full"></div>
              <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
              <div class="w-3 h-3 bg-green-400 rounded-full"></div>
              <div class="flex-1 mx-4">
                <div class="bg-white rounded-lg px-4 py-2 text-sm text-gray-500 border">
                  app.workeem.ma
                </div>
              </div>
            </div>
          </div>

          <!-- Dashboard Screenshot -->
          <div class="relative overflow-hidden">
            <!-- Container qui s'adapte exactement à l'image -->
            <div class="relative w-full">
              <img
                src="images/dashboard-screenshot.png"
                alt="Interface Workeem - Tableau de bord de gestion d'espace de coworking"
                class="w-full h-auto block"
                loading="lazy"
                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
              />
              <!-- Fallback si l'image ne charge pas -->
              <div class="hidden w-full bg-gradient-to-br from-primary-50 to-primary-100 items-center justify-center" style="aspect-ratio: 16/9; min-height: 400px;">
                <div class="text-center">
                  <div class="w-16 h-16 bg-primary rounded-2xl mx-auto mb-4 flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                  </div>
                  <p class="text-primary font-semibold">Tableau de bord Workeem</p>
                  <p class="text-gray-600 text-sm">Interface de gestion complète</p>
                </div>
              </div>
            </div>
            <!-- Overlay gradient pour un effet plus stylé -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/5 via-transparent to-transparent pointer-events-none"></div>
          </div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute -top-4 -left-4 w-20 h-20 bg-primary-100 rounded-full opacity-60 animate-pulse"></div>
        <div class="absolute -bottom-8 -right-8 w-32 h-32 bg-gradient-to-br from-primary-200 to-primary-100 rounded-full opacity-40 animate-pulse delay-1000"></div>
      </div>
    </div>
  </div>
</section>

<!-- Features Section -->
<section id="features" class="py-24 bg-white" aria-labelledby="features-title">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-20">
      <h2 id="features-title" class="text-4xl md:text-5xl font-bold text-gray-900 mb-6 tracking-tight">
        Tout ce dont vous avez besoin
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto font-light">
        Une solution complète pour gérer efficacement votre espace de coworking
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      @for (feature of features; track feature.title) {
        <div class="bg-white border border-gray-100 rounded-2xl p-8 hover:shadow-2xl hover:border-gray-200 transition-all duration-500 transform hover:-translate-y-1">
          <div class="w-14 h-14 bg-gradient-to-br from-primary-100 to-primary-50 rounded-2xl flex items-center justify-center mb-6 shadow-sm">
            <svg class="w-7 h-7 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path [attr.d]="feature.icon" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-4 tracking-tight">{{ feature.title }}</h3>
          <p class="text-gray-600 leading-relaxed font-light">{{ feature.description }}</p>
        </div>
      }
    </div>
  </div>
</section>

<!-- Why Choose Workeem Section -->
<section id="why-choose" class="py-24 bg-gradient-to-br from-primary-50 via-white to-gray-50" aria-labelledby="why-choose-title">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-20">
      <h2 id="why-choose-title" class="text-4xl md:text-5xl font-bold text-gray-900 mb-6 tracking-tight">
        Pourquoi choisir Workeem ?
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto font-light">
        Une solution pensée par des experts du coworking pour des gestionnaires d'espaces
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      @for (benefit of benefits; track benefit.title) {
        <div class="bg-white/70 backdrop-blur-sm border border-white/20 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1">
          <div class="w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-50 rounded-2xl flex items-center justify-center mb-6 shadow-sm">
            <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path [attr.d]="benefit.icon" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
            </svg>
          </div>
          <h3 class="text-2xl font-semibold text-gray-900 mb-4 tracking-tight">{{ benefit.title }}</h3>
          <p class="text-gray-700 text-lg leading-relaxed font-light">{{ benefit.description }}</p>
        </div>
      }
    </div>
  </div>
</section>

<!-- Pricing Section -->
<section id="pricing" class="py-24 bg-white" aria-labelledby="pricing-title">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-20">
      <h2 id="pricing-title" class="text-4xl md:text-5xl font-bold text-gray-900 mb-6 tracking-tight">
        Choisissez votre formule
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto font-light">
        Des tarifs transparents adaptés à la taille de votre espace
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
      @for (plan of pricingPlans; track plan.name) {
        <div class="relative bg-white rounded-3xl border border-gray-100 hover:border-gray-200 transition-all duration-500 transform hover:-translate-y-2 hover:shadow-2xl"
             [class.border-primary]="plan.popular"
             [class.shadow-xl]="plan.popular"
             [class.scale-105]="plan.popular">
          @if (plan.popular) {
            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <span class="bg-gradient-to-r from-primary to-primary-600 text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg">
                Le plus populaire
              </span>
            </div>
          }

          <div class="p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-2 tracking-tight">{{ plan.name }}</h3>
            <p class="text-gray-600 mb-8 font-light">{{ plan.description }}</p>

            <div class="mb-8 text-center">
              <div class="flex items-baseline justify-center gap-2">
                <span class="text-lg text-gray-400 line-through font-light">{{ plan.originalPrice }}</span>
                <span class="text-5xl font-bold text-gray-900 tracking-tight">{{ plan.price }}</span>
                <span class="text-lg text-gray-600 font-medium">MAD</span><span class="text-sm text-gray-500 font-light">/mois</span>
              </div>
            </div>

            <a href="#contact-form" class="w-full py-4 px-6 rounded-full font-semibold text-lg transition-all duration-300 shadow-sm hover:shadow-lg transform hover:-translate-y-0.5 inline-block text-center"
                    [class.bg-primary]="plan.popular"
                    [class.text-white]="plan.popular"
                    [class.hover:bg-primary-600]="plan.popular"
                    [class.border-2]="!plan.popular"
                    [class.border-gray-300]="!plan.popular"
                    [class.text-gray-700]="!plan.popular"
                    [class.hover:border-primary]="!plan.popular"
                    [class.hover:text-primary]="!plan.popular">
              Commencer
            </a>

            <ul class="mt-10 space-y-4">
              @for (feature of plan.features; track feature) {
                <li class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                  </svg>
                  <span class="text-gray-600 font-light">{{ feature }}</span>
                </li>
              }
            </ul>
          </div>
        </div>
      }
    </div>

    <!-- Pack Basic - Full Width -->
    <div class="mt-16 max-w-6xl mx-auto">
      <div class="bg-white rounded-3xl border border-gray-100 hover:border-gray-200 transition-all duration-500 transform hover:-translate-y-2 hover:shadow-2xl p-8">
        <div class="flex flex-col lg:flex-row items-center gap-8">
          <!-- Left side - Info -->
          <div class="flex-1 text-center lg:text-left">
            <h3 class="text-3xl font-bold text-gray-900 mb-2 tracking-tight">{{ basicPlan.name }}</h3>
            <p class="text-gray-600 mb-6 font-light">{{ basicPlan.description }}</p>

            <div class="mb-6">
              <div class="flex items-baseline justify-center lg:justify-start gap-2">
                <span class="text-lg text-gray-400 line-through font-light">{{ basicPlan.originalPrice }}</span>
                <span class="text-5xl font-bold text-gray-900 tracking-tight">{{ basicPlan.price }}</span>
                <span class="text-lg text-gray-600 font-medium">MAD</span><span class="text-sm text-gray-500 font-light">/mois</span>
              </div>
            </div>

            <a href="#contact-form" class="bg-primary hover:bg-primary-600 text-white py-4 px-8 rounded-full font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 inline-block">
              Commencer
            </a>
          </div>

          <!-- Right side - Features -->
          <div class="flex-1">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              @for (feature of basicPlan.features; track feature) {
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                  </svg>
                  <span class="text-gray-600 font-light">{{ feature }}</span>
                </div>
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Contact Form Section -->
<section id="contact-form" class="py-24 bg-white">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6 tracking-tight">
        Prêt à transformer votre espace ?
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto font-light">
        Laissez-nous vos coordonnées et découvrez comment Workeem peut révolutionner la gestion de votre coworking
      </p>
    </div>

    <div class="bg-gradient-to-br from-primary-50 to-white rounded-3xl p-8 md:p-12 shadow-xl border border-gray-100">
      <!-- Messages de statut -->
      @if (isSubmitted) {
        <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-xl">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            <span class="font-semibold">Message envoyé avec succès !</span>
          </div>
          <p class="mt-2 text-sm">Nous vous recontacterons dans les plus brefs délais.</p>
        </div>
      }

      @if (submitError) {
        <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-xl">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
            </svg>
            <span class="font-semibold">Erreur lors de l'envoi</span>
          </div>
          <p class="mt-2 text-sm">Veuillez réessayer ou nous contacter directement.</p>
        </div>
      }

      <form #contactFormRef="ngForm" (ngSubmit)="onSubmitForm($event, contactFormRef)" novalidate class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="firstName" class="block text-sm font-semibold text-gray-900 mb-2">Prénom *</label>
            <input type="text" id="firstName" name="firstName" required
                   [(ngModel)]="contactForm.firstName"
                   #firstName="ngModel"
                   (focus)="resetMessages()"
                   class="w-full px-4 py-3 rounded-xl border transition-all duration-300 bg-white backdrop-blur-sm focus:ring-2 focus:ring-primary focus:ring-opacity-20"
                   [class.border-red-300]="firstName.invalid && firstName.touched"
                   [class.border-gray-200]="firstName.valid || !firstName.touched"
                   [class.focus:border-primary]="firstName.valid || !firstName.touched">
            @if (firstName.invalid && firstName.touched) {
              <p class="text-red-500 text-sm mt-1">Le prénom est requis</p>
            }
          </div>
          <div>
            <label for="lastName" class="block text-sm font-semibold text-gray-900 mb-2">Nom *</label>
            <input type="text" id="lastName" name="lastName" required
                   [(ngModel)]="contactForm.lastName"
                   #lastName="ngModel"
                   (focus)="resetMessages()"
                   class="w-full px-4 py-3 rounded-xl border transition-all duration-300 bg-white backdrop-blur-sm focus:ring-2 focus:ring-primary focus:ring-opacity-20"
                   [class.border-red-300]="lastName.invalid && lastName.touched"
                   [class.border-gray-200]="lastName.valid || !lastName.touched"
                   [class.focus:border-primary]="lastName.valid || !lastName.touched">
            @if (lastName.invalid && lastName.touched) {
              <p class="text-red-500 text-sm mt-1">Le nom est requis</p>
            }
          </div>
        </div>

        <div>
          <label for="email" class="block text-sm font-semibold text-gray-900 mb-2">Email professionnel *</label>
          <input type="email" id="email" name="email" required
                 [(ngModel)]="contactForm.email"
                 #email="ngModel"
                 (focus)="resetMessages()"
                 class="w-full px-4 py-3 rounded-xl border transition-all duration-300 bg-white backdrop-blur-sm focus:ring-2 focus:ring-primary focus:ring-opacity-20"
                 [class.border-red-300]="email.invalid && email.touched"
                 [class.border-gray-200]="email.valid || !email.touched"
                 [class.focus:border-primary]="email.valid || !email.touched">
          @if (email.invalid && email.touched) {
            <p class="text-red-500 text-sm mt-1">
              @if (email.errors?.['required']) {
                L'email est requis
              } @else if (email.errors?.['email']) {
                Veuillez saisir un email valide
              }
            </p>
          }
        </div>

        <div>
          <label for="company" class="block text-sm font-semibold text-gray-900 mb-2">Nom de votre espace *</label>
          <input type="text" id="company" name="company" required
                 [(ngModel)]="contactForm.company"
                 #company="ngModel"
                 (focus)="resetMessages()"
                 class="w-full px-4 py-3 rounded-xl border transition-all duration-300 bg-white backdrop-blur-sm focus:ring-2 focus:ring-primary focus:ring-opacity-20"
                 [class.border-red-300]="company.invalid && company.touched"
                 [class.border-gray-200]="company.valid || !company.touched"
                 [class.focus:border-primary]="company.valid || !company.touched">
          @if (company.invalid && company.touched) {
            <p class="text-red-500 text-sm mt-1">Le nom de l'espace est requis</p>
          }
        </div>

        <div>
          <label for="phone" class="block text-sm font-semibold text-gray-900 mb-2">Téléphone *</label>
          <input type="tel" id="phone" name="phone" required
                 [(ngModel)]="contactForm.phone"
                 #phone="ngModel"
                 (focus)="resetMessages()"
                 class="w-full px-4 py-3 rounded-xl border transition-all duration-300 bg-white backdrop-blur-sm focus:ring-2 focus:ring-primary focus:ring-opacity-20"
                 [class.border-red-300]="phone.invalid && phone.touched"
                 [class.border-gray-200]="phone.valid || !phone.touched"
                 [class.focus:border-primary]="phone.valid || !phone.touched">
          @if (phone.invalid && phone.touched) {
            <p class="text-red-500 text-sm mt-1">Le téléphone est requis</p>
          }
        </div>

        <div>
          <label for="message" class="block text-sm font-semibold text-gray-900 mb-2">Message</label>
          <textarea id="message" name="message" rows="4"
                    [(ngModel)]="contactForm.message"
                    (focus)="resetMessages()"
                    class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-all duration-300 bg-white backdrop-blur-sm resize-none"
                    placeholder="Parlez-nous de votre projet, vos besoins, le nombre de postes..."></textarea>
        </div>

        <div class="flex items-start">
          <input type="checkbox" id="consent" name="consent" required
                 [(ngModel)]="contactForm.consent"
                 #consent="ngModel"
                 class="mt-1 w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary">
          <label for="consent" class="ml-3 text-sm text-gray-600 font-light">
            J'accepte d'être contacté par l'équipe Workeem concernant ma demande *
          </label>
          @if (consent.invalid && consent.touched) {
            <p class="text-red-500 text-sm mt-1 ml-7">Vous devez accepter d'être contacté</p>
          }
        </div>

        <div class="text-center pt-4">
          <button type="submit"
                  [disabled]="isSubmitting || contactFormRef.invalid"
                  class="bg-primary hover:bg-primary-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-12 py-4 rounded-full font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none disabled:shadow-none">
            @if (isSubmitting) {
              <span class="flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Envoi en cours...
              </span>
            } @else {
              Demander une démo
            }
          </button>
          @if (contactFormRef.invalid && contactFormRef.touched) {
            <p class="text-red-500 text-sm mt-2">Veuillez corriger les erreurs ci-dessus</p>
          }
          <p class="text-sm text-gray-500 mt-4 font-light">
            Réponse sous 24h • Démo personnalisée • Sans engagement
          </p>

        </div>
      </form>
    </div>
  </div>
</section>

<!-- Footer -->
<footer class="bg-gray-900 text-white py-20">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-12">
      <!-- Logo and description -->
      <div class="col-span-1 md:col-span-2">
        <div class="mb-6">
          <img class="h-12 w-auto footer-logo" src="images/logo.png" alt="Workeem">
        </div>
        <p class="text-gray-400 mb-8 max-w-md font-light text-lg leading-relaxed">
          La solution complète pour gérer votre espace de coworking avec simplicité et efficacité.
        </p>
        <!-- Réseaux sociaux -->
        <div class="flex space-x-6">
          <a href="https://www.instagram.com/workeem.ma?utm_source=ig_web_button_share_sheet&igsh=ZDNlZDc0MzIxNw==" target="_blank" class="text-gray-400 hover:text-white transition-all duration-300 transform hover:scale-110">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
            </svg>
          </a>
        </div>
      </div>

      <!-- Links -->
      <div>
        <h3 class="text-lg font-semibold mb-6 text-white">Produit</h3>
        <ul class="space-y-3">
          <li><a href="#features" class="text-gray-400 hover:text-white transition-all duration-300 font-light">Fonctionnalités</a></li>
          <li><a href="#pricing" class="text-gray-400 hover:text-white transition-all duration-300 font-light">Tarifs</a></li>
          <li><a href="#why-choose" class="text-gray-400 hover:text-white transition-all duration-300 font-light">Pourquoi choisir Workeem ?</a></li>
          <li><a href="https://eworkeem.netlify.app" target="_blank" class="text-gray-400 hover:text-white transition-all duration-300 font-light">Se connecter</a></li>
        </ul>
      </div>

      <div>
        <h3 class="text-lg font-semibold mb-6 text-white">Support</h3>
        <ul class="space-y-3">
          <li><a href="#contact-form" class="text-gray-400 hover:text-white transition-all duration-300 font-light">Contact</a></li>
          <li><a href="#contact-form" class="text-gray-400 hover:text-white transition-all duration-300 font-light">Démo</a></li>
        </ul>
      </div>
    </div>

    <div class="border-t border-gray-800 mt-16 pt-8 text-center">
      <p class="text-gray-400 font-light">
        © 2024 Workeem. Tous droits réservés.
      </p>
    </div>
  </div>
</footer>

<router-outlet />
