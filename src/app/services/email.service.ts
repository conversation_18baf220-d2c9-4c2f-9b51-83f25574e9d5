import { Injectable } from '@angular/core';
import emailjs from '@emailjs/browser';
import { EMAILJS_CONFIG } from '../config/emailjs.config';

export interface ContactForm {
  firstName: string;
  lastName: string;
  email: string;
  company?: string;
  phone?: string;
  message?: string;
}

@Injectable({
  providedIn: 'root'
})
export class EmailService {
  private serviceId = EMAILJS_CONFIG.SERVICE_ID;
  private templateId = EMAILJS_CONFIG.TEMPLATE_ID;
  private publicKey = EMAILJS_CONFIG.PUBLIC_KEY;

  constructor() {
    // Initialiser EmailJS avec votre clé publique
    emailjs.init(this.publicKey);
  }

  async sendContactForm(formData: ContactForm): Promise<boolean> {
    try {
      // Vérification des clés de configuration
      if (this.serviceId === 'YOUR_SERVICE_ID' ||
          this.templateId === 'YOUR_TEMPLATE_ID' ||
          this.publicKey === 'YOUR_PUBLIC_KEY') {
        console.error('EmailJS non configuré. Veuillez configurer vos clés dans emailjs.config.ts');
        throw new Error('EmailJS non configuré');
      }

      const templateParams = {
        from_name: `${formData.firstName} ${formData.lastName}`,
        from_email: formData.email,
        company: formData.company || '',
        phone: formData.phone || '',
        message: formData.message || '',
        to_name: 'Équipe Workeem',
        date: new Date().toLocaleDateString('fr-FR')
      };

      console.log('Envoi avec les paramètres:', templateParams);
      console.log('Configuration:', {
        serviceId: this.serviceId,
        templateId: this.templateId,
        publicKey: this.publicKey.substring(0, 5) + '...'
      });

      const response = await emailjs.send(
        this.serviceId,
        this.templateId,
        templateParams
      );

      console.log('Email envoyé avec succès:', response);
      return response.status === 200;
    } catch (error: any) {
      console.error('Erreur détaillée lors de l\'envoi de l\'email:', error);

      // Log des détails de l'erreur pour le débogage
      if (error.text) {
        console.error('Message d\'erreur:', error.text);
      }
      if (error.status) {
        console.error('Status de l\'erreur:', error.status);
      }

      return false;
    }
  }
}
