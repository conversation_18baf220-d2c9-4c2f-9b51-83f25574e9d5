import { Injectable } from '@angular/core';
import { ContactForm } from './email.service';

@Injectable({
  providedIn: 'root'
})
export class EmailTestService {

  async sendContactForm(formData: ContactForm): Promise<boolean> {
    // Simulation d'un délai d'envoi
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Log des données pour vérification
    console.log('🧪 MODE TEST - Données du formulaire:', {
      nom: `${formData.firstName} ${formData.lastName}`,
      email: formData.email,
      entreprise: formData.company || '(non renseigné)',
      telephone: formData.phone || '(non renseigné)',
      message: formData.message || '(aucun message)',
      date: new Date().toLocaleString('fr-FR')
    });

    // Simulation d'un succès (toujours réussir en mode test)
    const success = true; // Toujours réussir pour les tests

    if (success) {
      console.log('✅ Email simulé envoyé avec succès !');
    } else {
      console.log('❌ Échec simulé de l\'envoi d\'email');
    }

    return success;
  }
}
