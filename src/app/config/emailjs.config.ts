// Configuration EmailJS pour Workeem
// ⚠️ IMPORTANT: Remplacez ces valeurs par vos vraies clés EmailJS

export const EMAILJS_CONFIG = {
  // Votre Service ID (ex: 'service_abc123')
  SERVICE_ID: 'service_urhcwcp',

  // Votre Template ID (ex: 'template_xyz789')
  TEMPLATE_ID: 'template_2jf5rnd',

  // Votre Public Key (ex: 'user_abcdef123456')
  PUBLIC_KEY: 'TSOCQL20Zn6HSEHuJ'
};

/*
INSTRUCTIONS POUR CONFIGURER EMAILJS:

1. Créez un compte sur https://www.emailjs.com/
2. Créez un service email (Gmail, Outlook, etc.)
3. Créez un template avec ces variables:
   - {{from_name}} : Nom complet du contact
   - {{from_email}} : Email du contact
   - {{company}} : Nom de l'espace de coworking
   - {{phone}} : Téléphone du contact
   - {{message}} : Message du contact
   - {{to_name}} : "Équipe Workeem"

4. Remplacez les valeurs ci-dessus par vos vraies clés
5. Testez l'envoi depuis votre dashboard EmailJS

Exemple de template EmailJS:
---
Sujet: Nouvelle demande de démo - {{company}}

Bonjour {{to_name}},

Vous avez reçu une nouvelle demande de démo de la part de :

Nom: {{from_name}}
Email: {{from_email}}
Entreprise: {{company}}
Téléphone: {{phone}}

Message:
{{message}}

Cordialement,
Le formulaire de contact Workeem
---
*/
