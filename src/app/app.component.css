/* Styles personnalisés pour Workeem */
.hero-gradient {
  background: linear-gradient(135deg, #EDE9F8 0%, #ffffff 100%);
}

/* Animations douces */
.transition-all {
  transition: all 0.3s ease;
}

/* Effet hover sur les cartes */
.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Logo blanc dans le footer */
.footer-logo {
  filter: brightness(0) invert(1);
}

/* Hero Dashboard 3D Effect */
.perspective-1000 {
  perspective: 1000px;
}

.rotate-x-2 {
  transform: rotateX(2deg);
}

.rotate-x-0:hover {
  transform: rotateX(0deg);
}

/* Animations pour le dashboard */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes pulse-slow {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.8; }
}

.animate-pulse-slow {
  animation: pulse-slow 3s ease-in-out infinite;
}

/* Gradient animé pour la ligne du graphique */
@keyframes draw-line {
  0% { stroke-dasharray: 0 1000; }
  100% { stroke-dasharray: 1000 0; }
}

.animate-draw {
  animation: draw-line 2s ease-in-out;
}

/* Responsive aspect ratio pour les navigateurs plus anciens */
@supports not (aspect-ratio: 16/9) {
  .hero-image-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 ratio = 9/16 = 0.5625 */
  }

  .hero-image-container img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
