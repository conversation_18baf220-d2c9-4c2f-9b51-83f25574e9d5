import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { EmailService, ContactForm } from './services/email.service';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, FormsModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css',
  styles: [`
    /* Animation pour l'icône hamburger */
    .hamburger-line {
      transition: all 0.3s ease;
    }
  `]
})
export class AppComponent {
  title = 'workeem-landing';

  // Données du formulaire
  contactForm: ContactForm & { consent: boolean } = {
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    phone: '',
    message: '',
    consent: false
  };

  // États du formulaire
  isSubmitting = false;
  isSubmitted = false;
  submitError = false;

  // État du menu mobile
  isMobileMenuOpen = false;


  constructor(private emailService: EmailService) {}

  features = [
    {
      title: 'Tableau de bord',
      description: 'Vue d\'ensemble complète de votre espace avec statistiques en temps réel et indicateurs clés.',
      icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
    },
    {
      title: 'Gestion des membres',
      description: 'Gérez facilement vos membres, leurs profils, historiques et communications.',
      icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z'
    },
    {
      title: 'Abonnements & formules',
      description: 'Créez et gérez différents types d\'abonnements adaptés à vos besoins.',
      icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
    },
    {
      title: 'Gestion des espaces',
      description: 'Organisez vos espaces de travail, salles de réunion et zones communes efficacement.',
      icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4'
    },
    {
      title: 'Réservations',
      description: 'Système de réservation intuitif pour tous vos espaces avec calendrier intégré.',
      icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
    },
    {
      title: 'Facturation & paiements',
      description: 'Automatisez vos factures et gérez les paiements en toute sécurité.',
      icon: 'M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z'
    }
  ];

  benefits = [
    {
      title: 'Simplicité d\'usage',
      description: 'Interface intuitive pensée pour les gestionnaires d\'espaces, sans formation complexe nécessaire.',
      icon: 'M13 10V3L4 14h7v7l9-11h-7z'
    },
    {
      title: 'Gain de temps',
      description: 'Automatisez vos tâches répétitives et concentrez-vous sur le développement de votre communauté.',
      icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'
    },
    {
      title: 'Support expert',
      description: 'Équipe dédiée qui comprend les enjeux du coworking et vous accompagne dans votre croissance.',
      icon: 'M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z'
    }
  ];

  pricingPlans = [
    {
      name: 'Starter',
      description: 'Parfait pour débuter',
      price: '349',
      originalPrice: '449',
      popular: false,
      features: [
        'Jusqu\'à 50 membres',
        'Gestion des espaces de base',
        'Réservations simples',
        'Facturation automatisée',
        'Abonnements & formules',
        'Support email',
        'Tableau de bord basique'
      ]
    },
    {
      name: 'Professional',
      description: 'Pour les espaces en croissance',
      price: '699',
      originalPrice: '899',
      popular: true,
      features: [
        'Jusqu\'à 200 membres',
        'Gestion avancée des espaces',
        'Réservations avec calendrier',
        'Facturation automatisée',
        'Analytics détaillées',
        'Support prioritaire',
        'Intégrations tierces'
      ]
    },
    {
      name: 'Enterprise',
      description: 'Pour les grands espaces',
      price: '1099',
      originalPrice: '1399',
      popular: false,
      features: [
        'Membres illimités',
        'Multi-sites',
        'API complète',
        'Personnalisation avancée',
        'Support dédié 24/7',
        'Formation incluse',
        'SLA garanti'
      ]
    }
  ];

  basicPlan = {
    name: 'Basic',
    description: 'Solution complète pour débuter',
    price: '349',
    originalPrice: '449',
    features: [
      'Jusqu\'à 50 membres',
      'Gestion des espaces de base',
      'Réservations simples',
      'Support email',
      'Tableau de bord basique',
      'Analytics simples'
    ]
  };

  // Méthode pour soumettre le formulaire
  async onSubmitForm(event: Event, form: any) {
    event.preventDefault();

    console.log('🔍 État du formulaire:', {
      valid: form.valid,
      invalid: form.invalid,
      touched: form.touched,
      errors: form.errors,
      isSubmitting: this.isSubmitting
    });

    if (this.isSubmitting) {
      console.log('⏳ Soumission déjà en cours, abandon');
      return;
    }

    if (form.invalid) {
      console.log('❌ Formulaire invalide, abandon');
      console.log('📋 Erreurs du formulaire:', form.errors);
      return;
    }

    this.isSubmitting = true;
    this.submitError = false;

    try {
      console.log('🚀 Début de l\'envoi du formulaire');
      console.log('📋 Données du formulaire:', this.contactForm);
      console.log('✅ Validation du formulaire:', form.valid);

      // Utiliser le vrai service EmailJS
      console.log('📧 Appel du service EmailJS...');
      const success = await this.emailService.sendContactForm(this.contactForm);

      console.log('📊 Résultat du service:', success);

      if (success) {
        console.log('✅ Succès - Affichage du message de confirmation');
        this.isSubmitted = true;
        // Réinitialiser le formulaire
        this.contactForm = {
          firstName: '',
          lastName: '',
          email: '',
          company: '',
          phone: '',
          message: '',
          consent: false
        };
        form.resetForm();
        console.log('🔄 Formulaire réinitialisé');
      } else {
        console.log('❌ Échec - Affichage du message d\'erreur');
        this.submitError = true;
      }
    } catch (error) {
      console.error('💥 Erreur lors de la soumission:', error);
      console.error('📍 Stack trace:', error);
      this.submitError = true;
    } finally {
      console.log('🏁 Fin du processus d\'envoi');
      this.isSubmitting = false;
    }
  }

  // Méthode pour réinitialiser les messages
  resetMessages() {
    this.isSubmitted = false;
    this.submitError = false;
  }

  // Méthodes pour le menu mobile
  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeMobileMenu() {
    this.isMobileMenuOpen = false;
  }

  // Méthode pour scroll vers le haut
  scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    this.closeMobileMenu(); // Fermer le menu mobile si ouvert
  }
}
