# 🚀 Workeem Landing Page

Landing page moderne et élégante pour **Workeem**, le SaaS de gestion d'espaces de coworking avec un design Apple-like.

## ✨ Caractéristiques

### 🎨 Design Apple-like
- **Typographie** : SF Pro Display/Text avec -apple-system
- **Couleurs** : <PERSON><PERSON> (#6E56CF) avec nuances Apple
- **Animations** : Transitions fluides et micro-interactions
- **Glassmorphism** : Effets de transparence et backdrop-blur
- **Shadows** : Ombres subtiles inspirées d'Apple

### 🏗️ Architecture
- **Angular 19** avec standalone components
- **Tailwind CSS** avec configuration personnalisée
- **TypeScript** avec typage strict
- **Responsive Design** mobile-first
- **SSR Ready** avec Angular Universal

### 📱 Sections
1. **Header** - Navigation sticky avec logo et menu
2. **Hero** - Présentation principale avec CTA
3. **Features** - 6 fonctionnalités principales
4. **Testimonials** - Avis clients avec étoiles
5. **Pricing** - 3 plans tarifaires
6. **Footer** - Liens et informations

## 🛠️ Installation

```bash
# Cloner le projet
git clone [repository-url]
cd workeem-landing

# Installer les dépendances
npm install

# Lancer le serveur de développement
npm start
```

## 🚀 Développement

```bash
# Serveur de développement
ng serve --port 4201

# Build de production
ng build --prod

# Tests
ng test

# Linting
ng lint
```

## 🎨 Personnalisation

### Couleurs
Modifiez les couleurs dans `tailwind.config.js` :
```javascript
colors: {
  primary: {
    DEFAULT: '#6E56CF', // Votre couleur principale
    50: '#EDE9F8',      // Version claire
    // ...
  }
}
```

### Contenu
Modifiez les données dans `src/app/app.component.ts` :
- `features` : Fonctionnalités
- `testimonials` : Témoignages
- `pricingPlans` : Plans tarifaires

## 📦 Structure

```
src/
├── app/
│   ├── app.component.html    # Template principal
│   ├── app.component.ts      # Logique et données
│   └── app.component.css     # Styles spécifiques
├── styles.css                # Styles globaux
└── public/
    └── images/               # Assets statiques
```

## 🌟 Fonctionnalités Techniques

- **Hot Module Replacement** activé
- **Lazy Loading** prêt
- **PWA Ready** (à configurer)
- **SEO Optimized** avec meta tags
- **Accessibility** conforme WCAG
- **Performance** optimisée

## 📱 Responsive Breakpoints

- **Mobile** : < 768px
- **Tablet** : 768px - 1024px
- **Desktop** : > 1024px

## 🎯 Performance

- **Lighthouse Score** : 95+
- **First Contentful Paint** : < 1.5s
- **Largest Contentful Paint** : < 2.5s
- **Cumulative Layout Shift** : < 0.1

## 🔧 Configuration

### Tailwind CSS
Configuration personnalisée dans `tailwind.config.js` avec :
- Couleurs Apple-like
- Typographie SF Pro
- Animations personnalisées
- Utilitaires étendus

### Angular
- Standalone components
- OnPush change detection
- Lazy loading modules
- Tree shaking optimisé

## 📄 License

MIT License - voir le fichier LICENSE pour plus de détails.

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📞 Support

Pour toute question ou support :
- Email : <EMAIL>
- Documentation : [docs.workeem.com](https://docs.workeem.com)
- Issues : [GitHub Issues](https://github.com/workeem/landing/issues)

---

Fait avec ❤️ par l'équipe Workeem
